#include "ui.hpp"
#include <Windows.h>
#include <string>
#include <thread>
#include <chrono>
#include <cmath>
#include <iostream>
#include <filesystem>
#include <mutex>
#include <atomic>

#include "../auth/auth.hpp"
#include "../auth/utils.hpp"
#include "../auth/skStr.h"

#include "../globals.hpp"
#include "../security/config.hpp"
#include "../imgui/imgui.h"
#include "../imgui/imgui_internal.h"
#include "../security/debug.hpp"
#include "../security/strings.hpp"
#include "../security/macros.hpp"

#include "../font/font.hpp"

using namespace KeyAuth;

// Easing functions for smooth animations
float easeInOutCubic(float t) {
  return t < 0.5f ? 4.0f * t * t * t : 1.0f - pow(-2.0f * t + 2.0f, 3.0f) / 2.0f;
}

float easeInOutSine(float t) {
  return -(cos(3.14159f * t) - 1.0f) / 2.0f;
}

// KeyAuth configuration with enhanced security - ENCRYPTED
// Store encrypted values and decrypt at runtime
std::string name = skCrypt("Nebula").decrypt(); // App name
std::string ownerid = skCrypt("uRI84Vg5PZ").decrypt(); // Account ID
std::string version = skCrypt("1.0").decrypt(); // Application version. Used for automatic downloads see video here https://www.youtube.com/watch?v=kW195PLCBKs
std::string url = skCrypt("https://keyauth.win/api/1.3/").decrypt(); // change if using KeyAuth custom domains feature
std::string path = skCrypt("").decrypt(); // (OPTIONAL) see tutorial here https://www.youtube.com/watch?v=I9rxt821gMk&t=1s

api KeyAuthApp(name, ownerid, version, url, path);

// Authentication state variables with security
static bool keyauth_initialized = false;
static bool show_register = false;
static char license_buf[128] = {};
static char tfa_code_buf[16] = {};
static bool requires_2fa = false;
static std::string auth_error_message;
static std::string current_session_token;
static std::chrono::steady_clock::time_point last_activity;

// Threading and state management for asynchronous authentication
static std::mutex auth_mutex;
static std::atomic<bool> auth_completed(false);
static std::string auth_response_message;
static bool auth_success;

// Function to add KEYAUTH- prefix if not already present
std::string formatLicenseKey(const std::string& key) {
    if (key.empty()) return key;
    if (key.substr(0, 8) == skCrypt("KEYAUTH-").decrypt()) {
        return key; // Already has prefix
    }
    return skCrypt("KEYAUTH-").decrypt() + key;
}



// Username encoding/decoding functions
std::string generateUserHash(const std::string& username) {
    // Simple hash generation für den User
    uint32_t hash = 0;
    for (char c : username) {
        hash = hash * 31 + static_cast<uint32_t>(c);
    }

    std::stringstream ss;
    ss << skCrypt("CS_").decrypt() << std::hex << hash;
    return ss.str();
}

std::string encodeUsername(const std::string& real_username) {
    // Format: [HASH]_[USERNAME]
    std::string hash = generateUserHash(real_username);
    return hash + skCrypt("_").decrypt() + real_username;
}

std::string decodeUsername(const std::string& encoded_username) {
    #if defined(DEV)
    std::cout << "DECODE DEBUG: Input '" << encoded_username << "'\n";
    #endif

    // Finde den _ Separator
    size_t separator_pos = encoded_username.find('_');
    #if defined(DEV)
    std::cout << "DECODE DEBUG: Separator position: " << separator_pos << "\n";
    #endif

    if (separator_pos != std::string::npos && separator_pos > 0) {
        // Finde den LETZTEN _ (falls mehrere vorhanden)
        separator_pos = encoded_username.find_last_of('_');
        #if defined(DEV)
        std::cout << "DECODE DEBUG: Last separator position: " << separator_pos << "\n";
        #endif

        // Extrahiere den echten Username nach dem letzten _
        std::string extracted_username = encoded_username.substr(separator_pos + 1);
        #if defined(DEV)
        std::cout << "DECODE DEBUG: Extracted username: '" << extracted_username << "'\n";
        #endif

        // Extrahiere den Hash-Teil (alles vor dem letzten _)
        std::string hash_part = encoded_username.substr(0, separator_pos);
        #if defined(DEV)
        std::cout << "DECODE DEBUG: Hash part: '" << hash_part << "'\n";
        #endif

        // Validiere dass der Hash stimmt
        std::string expected_hash = generateUserHash(extracted_username);
        #if defined(DEV)
        std::cout << "DECODE DEBUG: Expected hash: '" << expected_hash << "'\n";
        #endif

        if (hash_part == expected_hash) {
            #if defined(DEV)
            std::cout << "DECODE DEBUG: Hash validation SUCCESS, returning '" << extracted_username << "'\n";
            #endif
            return extracted_username;
        } else {
            #if defined(DEV)
            std::cout << "DECODE DEBUG: Hash validation FAILED\n";
            #endif
        }
    }

    #if defined(DEV)
    std::cout << "DECODE DEBUG: Fallback, returning original '" << encoded_username << "'\n";
    #endif
    return encoded_username; // Fallback
}

// Case-sensitive username validation (without KeyAuth call)
bool validateUsernameCaseSensitive(const std::string& input_username) {
    #if defined(DEV)
    std::cout << "PRE-CHECK: User input '" << input_username << "' wird zu '" << encodeUsername(input_username) << "' encoded\n";
    #endif
    return true;
}

// Runs KeyAuth functions in a separate thread to prevent UI freezing
void auth_thread_func(bool is_register, std::string user, std::string pass, std::string key) {
    // Add security checks in authentication thread
    INTEGRITY_CHECK();
    TIMING_CHECK();
    FLOW_OBFUSCATE();

    if (is_register) {
        std::string formatted_key = formatLicenseKey(key);

        // REGISTER: Use encoded username
        std::string encoded_user = encodeUsername(user);
        #if defined(DEV)
        std::cout << "=== REGISTER ENCODING ===\n";
        std::cout << "Original: '" << user << "'\n";
        std::cout << "Hash: '" << generateUserHash(user) << "'\n";
        std::cout << "Encoded: '" << encoded_user << "'\n";
        std::cout << "=========================\n";
        #endif

        KeyAuthApp.regstr(encoded_user, pass, formatted_key);

        // Store credentials in JSON file if registration successful
        if (KeyAuthApp.response.success) {
            // Store the ORIGINAL username (not encoded) in local JSON
            WriteToJson(skCrypt("auth.json").decrypt(), skCrypt("username").decrypt(), user, true, skCrypt("password").decrypt(), pass);
        }
    }
    else {
        // CASE-SENSITIVE USERNAME CHECK BEFORE KEYAUTH LOGIN
        if (!validateUsernameCaseSensitive(user)) {
            // Username case doesn't match stored credentials
            // Fail silently with generic error message
            auth_success = false;
            auth_response_message = skCrypt("Invalid credentials").decrypt();
            return;
        }

        // Case validation passed, proceed with KeyAuth login using encoded username
        std::string encoded_user = encodeUsername(user);
        #if defined(DEV)
        std::cout << "=== ENCODING DEBUG ===\n";
        std::cout << "Original: '" << user << "'\n";
        std::cout << "Hash: '" << generateUserHash(user) << "'\n";
        std::cout << "Encoded: '" << encoded_user << "'\n";
        std::cout << "Length: " << encoded_user.length() << " chars\n";
        std::cout << "======================\n";
        #endif

        KeyAuthApp.login(encoded_user, pass);

        // Store credentials in JSON file if login successful
        if (KeyAuthApp.response.success) {
            // Decode the username from KeyAuth response
            std::string decoded_keyauth_user = decodeUsername(KeyAuthApp.user_data.username);

            #if defined(DEV)
            std::cout << "\n=== ENCODED USERNAME VALIDATION ===\n";
            std::cout << "User Input:     '" << user << "'\n";
            std::cout << "KeyAuth Raw:    '" << KeyAuthApp.user_data.username << "'\n";
            std::cout << "KeyAuth Decoded: '" << decoded_keyauth_user << "'\n";
            std::cout << "Match:          " << (user == decoded_keyauth_user ? "TRUE" : "FALSE") << "\n";
            std::cout << "===================================\n\n";
            #endif

            // CRITICAL: User input must match exactly what KeyAuth has registered (decoded)
            if (user != decoded_keyauth_user) {
                #if defined(DEV)
                std::cout << "❌ REJECTED: Input '" << user << "' != Registered '" << decoded_keyauth_user << "'\n";
                std::cout << "KeyAuth hat nur '" << decoded_keyauth_user << "' registriert (case-sensitive)!\n";
                #endif
                auth_success = false;
                auth_response_message = skCrypt("Invalid credentials").decrypt();
                KeyAuthApp.response.success = false;
                return;
            }

            #if defined(DEV)
            std::cout << "[!] ACCEPTED: '" << user << "' ist exakt richtig!\n";
            #endif
            WriteToJson(skCrypt("auth.json").decrypt(), skCrypt("username").decrypt(), user, true, skCrypt("password").decrypt(), pass);
            current_session_token = user + std::to_string(std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::steady_clock::now().time_since_epoch()).count());
            last_activity = std::chrono::steady_clock::now();
        }
    }

    // Safely update shared variables once the KeyAuth operation is complete
    std::lock_guard<std::mutex> lock(auth_mutex);
    auth_success = KeyAuthApp.response.success;
    auth_response_message = KeyAuthApp.response.message;
    auth_completed = true; // Signal that the authentication attempt is finished

    // Clear sensitive data from memory
    Obfuscation::MemoryProtection::ClearSensitiveData((void*)user.data(), user.size());
    Obfuscation::MemoryProtection::ClearSensitiveData((void*)pass.data(), pass.size());
    Obfuscation::MemoryProtection::ClearSensitiveData((void*)key.data(), key.size());
}

// Session management
std::string tm_to_readable_time(tm ctx);
static std::time_t string_to_timet(std::string timestamp);
static std::tm timet_to_tm(time_t timestamp);
void sessionStatus();

// Initialize KeyAuth with auto-login support
void initializeKeyAuth() {
  if (!keyauth_initialized) {
    INTEGRITY_CHECK();
    TIMING_CHECK();
    FLOW_OBFUSCATE();

    KeyAuthApp.init();
    keyauth_initialized = true;

    if (!KeyAuthApp.response.success) {
      auth_error_message = skCrypt("Failed to connect to Server").decrypt();
      return;
    }

    // Check for saved credentials in JSON file
    std::string auth_json_path = skCrypt("auth.json").decrypt();
    if (std::filesystem::exists(auth_json_path)) {
      if (CheckIfJsonKeyExists(auth_json_path, skCrypt("username").decrypt())) {
        std::string saved_username = ReadFromJson(auth_json_path, skCrypt("username").decrypt());
        std::string saved_password = ReadFromJson(auth_json_path, skCrypt("password").decrypt());

        // Set username in globals (preserves exact case)
        strcpy_s(globals.user_name, saved_username.c_str());
        strcpy_s(globals.pass_word, saved_password.c_str());

        // Auto-login with saved credentials
        std::thread(auth_thread_func, false, saved_username, saved_password, skCrypt("").decrypt()).detach();
        globals.login_in_progress = true;
      }
    }

    JUNK_CODE_1;
  }
}

void ui::render() {
  if (!globals.active) return;

  // Keep session alive during active use - update activity time every few seconds
  static auto last_activity_update = std::chrono::steady_clock::now();
  auto now = std::chrono::steady_clock::now();
  auto time_since_update = std::chrono::duration_cast<std::chrono::seconds>(now - last_activity_update);

  if (time_since_update.count() > 10) { // Update every 10 seconds during active use
    last_activity = now;
    last_activity_update = now;

    // Also refresh the session to keep it alive
    if (globals.logged_in && !current_session_token.empty()) {
      // Session is maintained by KeyAuth automatically
      last_activity = now;
    }
  }

  // Security checks on every render
  static int security_counter = 0;
  if (++security_counter % 100 == 0) {
    INTEGRITY_CHECK();
    TIMING_CHECK();
    FLOW_OBFUSCATE();
  }
  
  initializeKeyAuth();

  static char username_buf[64] = {};
  static char password_buf[64] = {};
  static bool show_password = false;
  
  // Session timeout check - Extended timeout and activity detection
  // User requested to fix program exiting after being logged in
  if (globals.logged_in && !current_session_token.empty()) {
    auto now = std::chrono::steady_clock::now();
    auto elapsed = std::chrono::duration_cast<std::chrono::hours>(now - last_activity);

    ImGui::CreateContext();
    ImGui::StyleColorsDark();

    // Detect user interactions to keep session alive
    ImGuiIO& io = ImGui::GetIO(); 
    (void)io;

    io.Fonts->AddFontFromMemoryTTF( Roboto_Medium, sizeof( Roboto_Medium ), 13.0f );

    bool user_active = io.MouseClicked[0] || io.MouseClicked[1] || io.MouseClicked[2] ||
                      io.KeysDownDuration[0] > 0 || io.MouseWheel != 0.0f ||
                      io.MouseDelta.x != 0.0f || io.MouseDelta.y != 0.0f;



    if (user_active) {
      last_activity = now; // Reset activity timer on any user interaction
    }

    if (elapsed.count() > 24) { // Extended to 24 hours instead of 30 minutes
      globals.logged_in = false;
      current_session_token.clear();

      // Remove saved credentials on session timeout
      std::string auth_json_path = skCrypt("auth.json").decrypt();
      if (std::filesystem::exists(auth_json_path)) {
        std::filesystem::remove(auth_json_path);
      }

      auth_error_message = skCrypt("Session expired after 24 hours, please login again").decrypt();
    }
  }
  
  JUNK_CODE_2;

  if (globals.login_in_progress) {
    globals.loading_rotation += ImGui::GetIO().DeltaTime * 6.0f;
  }

  // Check if an asynchronous authentication attempt has completed
  if (auth_completed) {
      globals.login_in_progress = false;

      std::lock_guard<std::mutex> lock(auth_mutex);
      if (auth_success) {
          globals.logged_in = true;
          strcpy_s(globals.user_name, KeyAuthApp.user_data.username.c_str());
          last_activity = std::chrono::steady_clock::now();
          
          if (show_register) {
              show_register = false;
          }
          std::thread(sessionStatus).detach();
      } else {
          if (auth_response_message == skCrypt("2FA code required.").decrypt()) {
              requires_2fa = true;
              auth_error_message = skCrypt("2FA code required").decrypt();
          } else if (show_register) {
              auth_error_message = skCrypt("Registration failed - please check your details").decrypt();
              SecureZeroMemory(username_buf, sizeof(username_buf));
              SecureZeroMemory(password_buf, sizeof(password_buf));
              SecureZeroMemory(license_buf, sizeof(license_buf));
          } else {
              // Generic error message - don't reveal specific issues
              auth_error_message = skCrypt("Login failed - invalid credentials").decrypt();
              SecureZeroMemory(username_buf, sizeof(username_buf));
              SecureZeroMemory(password_buf, sizeof(password_buf));
          }
      }
      auth_completed = false;
  }

  ImVec2 window_size_vec = globals.logged_in ? ImVec2(550, 400) : ImVec2(300, 255);
  ImGui::SetNextWindowSize(window_size_vec, ImGuiCond_Always);

  ImGui::StyleColorsDark();
  ImGui::PushStyleColor(ImGuiCol_WindowBg, ImVec4(0.06f, 0.06f, 0.06f, 0.94f));
  ImGui::PushStyleColor(ImGuiCol_FrameBg, ImVec4(0.15f, 0.15f, 0.15f, 1.0f));
  ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(0.2f, 0.2f, 0.2f, 1.0f));
  ImGui::PushStyleColor(ImGuiCol_ButtonHovered, ImVec4(0.3f, 0.3f, 0.3f, 1.0f));
  ImGui::PushStyleColor(ImGuiCol_ButtonActive, ImVec4(0.15f, 0.15f, 0.15f, 1.0f));
  ImGui::PushStyleColor(ImGuiCol_Border, ImVec4(0.4f, 0.4f, 0.4f, 0.5f));
  ImGui::PushStyleColor(ImGuiCol_Header, ImVec4(0.2f, 0.2f, 0.2f, 1.0f));
  ImGui::PushStyleColor(ImGuiCol_HeaderHovered, ImVec4(0.3f, 0.3f, 0.3f, 1.0f));
  ImGui::PushStyleColor(ImGuiCol_HeaderActive, ImVec4(0.15f, 0.15f, 0.15f, 1.0f));

  ImGui::PushStyleVar(ImGuiStyleVar_FrameRounding, 3.0f);
  ImGui::PushStyleVar(ImGuiStyleVar_WindowRounding, 3.0f);
  ImGui::PushStyleVar(ImGuiStyleVar_ChildRounding, 3.0f);
  ImGui::PushStyleVar(ImGuiStyleVar_GrabRounding, 3.0f);
  ImGui::PushStyleVar(ImGuiStyleVar_TabRounding, 3.0f);
  ImGui::PushStyleVar(ImGuiStyleVar_WindowBorderSize, 2.0f);
  ImGui::PushStyleVar(ImGuiStyleVar_WindowPadding, ImVec2(20.0f, 8.0f));

  ImGuiWindowFlags window_flags = ImGuiWindowFlags_NoResize | ImGuiWindowFlags_NoCollapse | ImGuiWindowFlags_NoScrollbar | ImGuiWindowFlags_NoSavedSettings;
  ImGui::SetNextWindowPos(ImVec2(window_pos.x, window_pos.y), ImGuiCond_Once);
  ImGui::SetNextWindowBgAlpha(1.0f);

  ImGui::Begin(skCrypt("Secure Login").decrypt(), &globals.active, window_flags);

  if (globals.login_in_progress) {
    ImVec2 window_size = ImGui::GetWindowSize();
    ImVec2 window_pos = ImGui::GetWindowPos();
    ImVec2 center = ImVec2(window_pos.x + window_size.x * 0.5f, window_pos.y + window_size.y * 0.5f);
    float radius = 20.0f;
    ImDrawList* draw_list = ImGui::GetWindowDrawList();
    for (int i = 0; i < 8; i++) {
      float angle = globals.loading_rotation + (i * 2.0f * 3.14159f / 8.0f);
      float alpha = 0.3f + 0.7f * (1.0f - (float)i / 8.0f);
      ImVec2 pos = ImVec2(center.x + cosf(angle) * radius, center.y + sinf(angle) * radius);
      ImU32 color = IM_COL32(100, 150, 255, (int)(alpha * 255));
      draw_list->AddCircleFilled(pos, 4.0f, color);
    }
    const char* auth_text = skCrypt("Authenticating...").decrypt();
    ImVec2 text_size = ImGui::CalcTextSize(auth_text);
    ImVec2 text_pos = ImVec2(center.x - text_size.x * 0.5f, center.y + 40.0f);
    draw_list->AddText(text_pos, IM_COL32(255, 255, 255, 255), auth_text);
  }

  ImGui::Spacing();
  ImGui::Spacing();

  ImVec2 window_size = ImGui::GetWindowSize();
  ImVec2 child_size = ImVec2(window_size.x - 40.0f, window_size.y - 120.0f);
  ImVec2 child_pos = ImVec2(20.0f, 30.0f);

  ImGui::SetCursorPos(child_pos);
  ImGui::BeginChild(skCrypt("ContentChild").decrypt(), child_size, false, ImGuiWindowFlags_NoScrollbar);
  if (!globals.login_in_progress) {
    ImGui::Spacing();
    float content_width = ImGui::GetContentRegionAvail().x;
    float item_width = 260.0f;

    if (globals.logged_in) {
      // LOGGED IN UI
      std::string success_msg = skCrypt("Logged in as ").decrypt() + (std::string(globals.user_name)) + skCrypt("!").decrypt();
      ImGui::TextColored(ImVec4(0.0f, 1.0f, 0.0f, 1.0f), "%s", success_msg.c_str());
      ImGui::Spacing();
      if (ImGui::BeginTabBar(skCrypt("MainTabs").decrypt(), ImGuiTabBarFlags_None)) {
        if (ImGui::BeginTabItem(skCrypt("Profile").decrypt())) {
            ImGui::Text(skCrypt("Profile Content").decrypt());
            // Display user info from KeyAuth
            ImGui::Text(skCrypt("Username: %s").decrypt(), KeyAuthApp.user_data.username.c_str());
            ImGui::Text(skCrypt("Hardware ID: %s").decrypt(), KeyAuthApp.user_data.hwid.substr(0, 8).c_str());
            ImGui::Text(skCrypt("Session: %s").decrypt(), current_session_token.substr(0, 8).c_str());
            ImGui::EndTabItem();
        }
        if (ImGui::BeginTabItem(skCrypt("Settings").decrypt())) {
            ImGui::Text(skCrypt("Settings Content").decrypt());

            // Popup Configuration Status
            ImGui::Separator();
            ImGui::Text(skCrypt("Popup Configuration:").decrypt());
            #if DISABLE_SECURITY_POPUPS
                ImGui::TextColored(ImVec4(1.0f, 0.5f, 0.0f, 1.0f), skCrypt("Security Popups: DISABLED").decrypt());
            #else
                ImGui::TextColored(ImVec4(0.0f, 1.0f, 0.0f, 1.0f), skCrypt("Security Popups: ENABLED").decrypt());
            #endif
            #if DISABLE_GUI_POPUPS
                ImGui::TextColored(ImVec4(1.0f, 0.5f, 0.0f, 1.0f), skCrypt("GUI Setup Popups: DISABLED").decrypt());
            #else
                ImGui::TextColored(ImVec4(0.0f, 1.0f, 0.0f, 1.0f), skCrypt("GUI Setup Popups: ENABLED").decrypt());
            #endif
            #if DISABLE_ERROR_POPUPS
                ImGui::TextColored(ImVec4(1.0f, 0.0f, 0.0f, 1.0f), skCrypt("Error Popups: DISABLED").decrypt());
            #else
                ImGui::TextColored(ImVec4(0.0f, 1.0f, 0.0f, 1.0f), skCrypt("Error Popups: ENABLED").decrypt());
            #endif
            #if DISABLE_INFO_POPUPS
                ImGui::TextColored(ImVec4(1.0f, 0.5f, 0.0f, 1.0f), skCrypt("Info Popups: DISABLED").decrypt());
            #else
                ImGui::TextColored(ImVec4(0.0f, 1.0f, 0.0f, 1.0f), skCrypt("Info Popups: ENABLED").decrypt());
            #endif
            ImGui::Text(skCrypt("Only errors will be shown when they occur").decrypt());
            ImGui::Separator();

            if (ImGui::Button(skCrypt("Clear Stored Credentials").decrypt())) {
                // Remove saved credentials JSON file
                std::string auth_json_path = skCrypt("auth.json").decrypt();
                if (std::filesystem::exists(auth_json_path)) {
                    std::filesystem::remove(auth_json_path);
                }
            }
            if (ImGui::Button(skCrypt("Logout").decrypt())) {
                globals.logged_in = false;
                current_session_token.clear();

                // Remove saved credentials on logout
                std::string auth_json_path = skCrypt("auth.json").decrypt();
                if (std::filesystem::exists(auth_json_path)) {
                    std::filesystem::remove(auth_json_path);
                }
            }
            ImGui::EndTabItem();
        }
        ImGui::EndTabBar();
      }
    } else if (requires_2fa) {
      // 2FA UI
      ImGui::Text(skCrypt("Enter 2FA Code:").decrypt());
      ImGui::Spacing();

      float available_width = ImMin(260.0f, ImGui::GetContentRegionAvail().x);
      float start_x = (ImGui::GetContentRegionAvail().x - available_width) * 0.5f;

      ImGui::SetCursorPosX(start_x);
      ImGui::PushItemWidth(available_width);
      ImGui::InputTextWithHint(skCrypt("##2fa_code").decrypt(), skCrypt("2FA Code").decrypt(), tfa_code_buf, sizeof(tfa_code_buf));
      ImGui::PopItemWidth();
      ImGui::Spacing();
      
      ImGui::SetCursorPosX(start_x);
      bool tfa_valid = strlen(tfa_code_buf) > 0;
      if (!tfa_valid) {
          ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(0.2f, 0.2f, 0.2f, 0.5f));
          ImGui::PushStyleColor(ImGuiCol_ButtonHovered, ImVec4(0.2f, 0.2f, 0.2f, 0.5f));
          ImGui::PushStyleColor(ImGuiCol_ButtonActive, ImVec4(0.2f, 0.2f, 0.2f, 0.5f));
      } else {
          ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(0.26f, 0.59f, 0.98f, 0.8f));
          ImGui::PushStyleColor(ImGuiCol_ButtonHovered, ImVec4(0.26f, 0.59f, 0.98f, 1.0f));
          ImGui::PushStyleColor(ImGuiCol_ButtonActive, ImVec4(0.06f, 0.53f, 0.98f, 1.0f));
      }
      
      if (ImGui::Button(skCrypt("Verify 2FA").decrypt(), ImVec2(available_width, 0)) && tfa_valid) {
          // CASE-SENSITIVE USERNAME CHECK for 2FA
          if (!validateUsernameCaseSensitive(std::string(globals.user_name))) {
              auth_error_message = skCrypt("Invalid credentials").decrypt();
              SecureZeroMemory(tfa_code_buf, sizeof(tfa_code_buf));
          } else {
              // Use encoded username for 2FA
              std::string encoded_user = encodeUsername(std::string(globals.user_name));
              #if defined(DEV)
              std::cout << "2FA LOGIN: '" << globals.user_name << "' -> '" << encoded_user << "'\n";
              #endif

              KeyAuthApp.login(encoded_user, std::string(globals.pass_word), std::string(tfa_code_buf));
              if (KeyAuthApp.response.success) {
                  // CRITICAL: Post-2FA case validation
                  std::string decoded_keyauth_user = decodeUsername(KeyAuthApp.user_data.username);

                  #if defined(DEV)
                  std::cout << "2FA VALIDATION: Input '" << globals.user_name << "' vs Decoded '" << decoded_keyauth_user << "'\n";
                  #endif

                  if (std::string(globals.user_name) != decoded_keyauth_user) {
                      #if defined(DEV)
                      std::cout << "POST-2FA VALIDATION FAILED: Input '" << globals.user_name << "' != Decoded '" << decoded_keyauth_user << "'\n";
                      #endif
                      auth_error_message = skCrypt("Invalid credentials").decrypt();
                      KeyAuthApp.response.success = false;
                  } else {
                      globals.logged_in = true;
                      requires_2fa = false;

                      // Store credentials after successful 2FA
                      WriteToJson(skCrypt("auth.json").decrypt(), skCrypt("username").decrypt(), std::string(globals.user_name), true, skCrypt("password").decrypt(), std::string(globals.pass_word));
                      current_session_token = std::string(globals.user_name) + std::to_string(std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::steady_clock::now().time_since_epoch()).count());
                      last_activity = std::chrono::steady_clock::now();
                  }
              } else {
                  auth_error_message = skCrypt("Invalid 2FA code").decrypt();
              }
              SecureZeroMemory(tfa_code_buf, sizeof(tfa_code_buf));
          }
      }
      ImGui::PopStyleColor(3);
    } else if (show_register) {
      // REGISTER UI
      float available_width = ImMin(item_width, content_width);
      float start_x = (content_width - available_width) * 0.5f;

      ImGui::SetCursorPosX(start_x);
      ImGui::PushItemWidth(available_width);
      ImGui::InputTextWithHint(skCrypt("##reg_username").decrypt(), skCrypt("Username").decrypt(), username_buf, sizeof(username_buf));
      ImGui::PopItemWidth();
      ImGui::Spacing();

      ImGui::SetCursorPosX(start_x);
      ImGui::PushItemWidth(ImMax(0.0f, available_width - 80.0f));
      ImGui::InputTextWithHint(skCrypt("##reg_password").decrypt(), skCrypt("Password").decrypt(), password_buf, sizeof(password_buf), show_password ? 0 : ImGuiInputTextFlags_Password);
      ImGui::PopItemWidth();
      ImGui::SameLine();
      if (ImGui::Button(show_password ? skCrypt("Hide").decrypt() : skCrypt("Show").decrypt(), ImVec2(70, 0))) { show_password = !show_password; }
      ImGui::Spacing();

      ImGui::SetCursorPosX(start_x);
      ImGui::PushItemWidth(available_width);
      ImGui::InputTextWithHint(skCrypt("##license").decrypt(), skCrypt("License Key").decrypt(), license_buf, sizeof(license_buf));
      ImGui::PopItemWidth();
      ImGui::Spacing();
      ImGui::Spacing();

      ImGui::SetCursorPosX(start_x);
      bool fields_valid = (strlen(username_buf) > 0 && strlen(password_buf) > 0 && strlen(license_buf) > 0);
      if (!fields_valid) { 
        ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(0.2f, 0.2f, 0.2f, 0.5f)); 
        ImGui::PushStyleColor(ImGuiCol_ButtonHovered, ImVec4(0.2f, 0.2f, 0.2f, 0.5f));
        ImGui::PushStyleColor(ImGuiCol_ButtonActive, ImVec4(0.2f, 0.2f, 0.2f, 0.5f));
      } else {
        ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(0.26f, 0.59f, 0.98f, 0.8f));
        ImGui::PushStyleColor(ImGuiCol_ButtonHovered, ImVec4(0.26f, 0.59f, 0.98f, 1.0f));
        ImGui::PushStyleColor(ImGuiCol_ButtonActive, ImVec4(0.06f, 0.53f, 0.98f, 1.0f));
      }

      if (ImGui::Button(skCrypt("Register").decrypt(), ImVec2(available_width, 0)) && fields_valid) {
        auth_error_message = "";
        globals.login_in_progress = true;
        std::thread(auth_thread_func, true, std::string(username_buf), std::string(password_buf), std::string(license_buf)).detach();
      }
      ImGui::PopStyleColor(3);
      ImGui::Spacing();

      ImGui::SetCursorPosX((content_width - ImGui::CalcTextSize(skCrypt("Have an account? Login").decrypt()).x) * 0.5f);
      ImGui::Text(skCrypt("Have an account? ").decrypt());
      ImGui::SameLine(0, 0);
      ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(0.4f, 0.7f, 1.0f, 1.0f));
      if (ImGui::Selectable(skCrypt("Login").decrypt(), false, ImGuiSelectableFlags_DontClosePopups, ImVec2(ImGui::CalcTextSize(skCrypt("Login").decrypt()).x, 0))) {
        show_register = false;
        auth_error_message = "";
      }
      if (ImGui::IsItemHovered()) { ImGui::SetMouseCursor(ImGuiMouseCursor_Hand); }
      ImGui::PopStyleColor();

    } else {
      // LOGIN UI
      float available_width = ImMin(item_width, content_width);
      float start_x = (content_width - available_width) * 0.5f;

      ImGui::SetCursorPosX(start_x);
      ImGui::PushItemWidth(available_width);
      ImGui::InputTextWithHint(skCrypt("##username").decrypt(), skCrypt("Username").decrypt(), username_buf, sizeof(username_buf));
      ImGui::PopItemWidth();
      ImGui::Spacing();

      ImGui::SetCursorPosX(start_x);
      ImGui::PushItemWidth(ImMax(0.0f, available_width - 80.0f));
      ImGui::InputTextWithHint(skCrypt("##password").decrypt(), skCrypt("Password").decrypt(), password_buf, sizeof(password_buf), show_password ? 0 : ImGuiInputTextFlags_Password);
      ImGui::PopItemWidth();
      ImGui::SameLine();
      if (ImGui::Button(show_password ? skCrypt("Hide").decrypt() : skCrypt("Show").decrypt(), ImVec2(70, 0))) { show_password = !show_password; }
      ImGui::Spacing();
      ImGui::Spacing();

      ImGui::SetCursorPosX(start_x);
      bool fields_valid = (strlen(username_buf) > 0 && strlen(password_buf) > 0);
      if (!fields_valid) { 
        ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(0.2f, 0.2f, 0.2f, 0.5f)); 
        ImGui::PushStyleColor(ImGuiCol_ButtonHovered, ImVec4(0.2f, 0.2f, 0.2f, 0.5f));
        ImGui::PushStyleColor(ImGuiCol_ButtonActive, ImVec4(0.2f, 0.2f, 0.2f, 0.5f));
      } else {
        ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(0.26f, 0.59f, 0.98f, 0.8f));
        ImGui::PushStyleColor(ImGuiCol_ButtonHovered, ImVec4(0.26f, 0.59f, 0.98f, 1.0f));
        ImGui::PushStyleColor(ImGuiCol_ButtonActive, ImVec4(0.06f, 0.53f, 0.98f, 1.0f));
      }

      if (ImGui::Button(skCrypt("Login").decrypt(), ImVec2(available_width, 0)) && fields_valid) {
        auth_error_message = "";
        globals.login_in_progress = true;
        last_activity = std::chrono::steady_clock::now();
        std::thread(auth_thread_func, false, std::string(username_buf), std::string(password_buf), skCrypt("").decrypt()).detach();
      }
      ImGui::PopStyleColor(3);
      ImGui::Spacing();

      ImGui::SetCursorPosX((content_width - ImGui::CalcTextSize(skCrypt("Don't have an account? Sign Up").decrypt()).x) * 0.5f);
      ImGui::Text(skCrypt("Don't have an account? ").decrypt());
      ImGui::SameLine(0, 0);
      ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(0.4f, 0.7f, 1.0f, 1.0f));
      if (ImGui::Selectable(skCrypt("Sign Up").decrypt(), false, ImGuiSelectableFlags_DontClosePopups, ImVec2(ImGui::CalcTextSize(skCrypt("Sign Up").decrypt()).x, 0))) {
        show_register = true;
        auth_error_message = "";
      }
      if (ImGui::IsItemHovered()) { ImGui::SetMouseCursor(ImGuiMouseCursor_Hand); }
      ImGui::PopStyleColor();
    }

    if (!auth_error_message.empty()) {
      ImGui::Spacing();
      ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(1.0f, 0.3f, 0.3f, 1.0f));
      float error_width = ImGui::CalcTextSize(auth_error_message.c_str()).x;
      ImGui::SetCursorPosX((content_width - error_width) * 0.5f);
      ImGui::TextWrapped("%s", auth_error_message.c_str());
      ImGui::PopStyleColor();
    }


  }
  ImGui::EndChild();
  ImGui::End();

  ImGui::PopStyleColor(9);
  ImGui::PopStyleVar(7);
}

void ui::init(LPDIRECT3DDEVICE9 device) {
  dev = device;
  if (window_pos.x == 0) {
    RECT screen_rect{};
    GetWindowRect(GetDesktopWindow(), &screen_rect);
    screen_res = ImVec2(float(screen_rect.right), float(screen_rect.bottom));
    window_pos = (screen_res - window_size) * 0.5f;
  }
}

void sessionStatus() {
  while (globals.logged_in && globals.active) {
    Sleep(60000); // Increased from 20 seconds to 60 seconds to reduce server load

    // Enhanced session validation
    INTEGRITY_CHECK();
    TIMING_CHECK();

    // Session is maintained by KeyAuth automatically
    // No additional session validation needed

    // KeyAuth check - FIXED: Don't exit program on auth server issues
    KeyAuthApp.check();
    if (!KeyAuthApp.response.success) {
      // FIXED: Don't set globals.active = false (this was causing program exit)
      // Instead, just log the user out but keep program running
      globals.logged_in = false;
      current_session_token.clear();

      // Remove saved credentials on auth failure
      std::string auth_json_path = skCrypt("auth.json").decrypt();
      if (std::filesystem::exists(auth_json_path)) {
        std::filesystem::remove(auth_json_path);
      }

      auth_error_message = skCrypt("Authentication server connection lost. Please login again.").decrypt();
      // Program continues running, user can login again
    } else {
      // Refresh session on successful check
      last_activity = std::chrono::steady_clock::now();
      // Clear any previous error messages on successful auth
      if (auth_error_message == skCrypt("Authentication server connection lost. Please login again.").decrypt()) {
        auth_error_message = "";
      }
    }

    JUNK_CODE_3;
  }
}

std::string tm_to_readable_time(tm ctx) {
  char buffer[80];
  strftime(buffer, sizeof(buffer), skCrypt("%a %m/%d/%y %H:%M:%S %Z").decrypt(), &ctx);
  return std::string(buffer);
}

static std::time_t string_to_timet(std::string timestamp) {
  auto cv = strtol(timestamp.c_str(), NULL, 10);
  return (time_t)cv;
}

static std::tm timet_to_tm(time_t timestamp) {
  std::tm context;
  localtime_s(&context, &timestamp);
  return context;
}
