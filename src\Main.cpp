#include "Main.hpp"
#include "ui/ui.hpp"
#include "globals.hpp"
#include <tlhelp32.h>
#include <iostream>
#include <winternl.h>  // For PPEB definition

// ADVANCED PROTECTION SYSTEM
#include "security/config.hpp"
#include "security/security.hpp"
#include "security/debug.hpp"
#include "security/strings.hpp"
#include "security/core.hpp"
#include "security/encryption.hpp"
#include "security/integrity.hpp"
#include "security/macros.hpp"
#include "security/obfuscation.hpp"
#include "security/protection.hpp"
#include "security/indirect_crash.hpp"
#include "auth/skStr.h"  // Add skCrypt support

// Main code
int APIENTRY WinMain(HINSTANCE, HINSTANCE, LPSTR, int)
{
    // IMMEDIATE TEST - Show that program starts
    INFO_POPUP(NULL,
        skCrypt("Program is starting...\n\nIf you see this message, the program can run.\nNext, security checks will be performed.\n\nClick OK to continue with security checks.").decrypt(),
        skCrypt("Secure Login - Starting").decrypt(),
        MB_OK | MB_ICONINFORMATION);

    // Create application window FIRST so user can see error messages
    const char* window_class = skCrypt("ImGuiLoaderClass").decrypt();  // Simple class name
    const char* window_title = skCrypt("Secure Login").decrypt();  // Window title
    WNDCLASSEXA wc = { sizeof(WNDCLASSEXA), CS_CLASSDC, WndProc, 0L, 0L, GetModuleHandle(NULL), NULL, NULL, NULL, NULL, window_class, NULL };

    if (!RegisterClassExA(&wc)) {
        DWORD error = GetLastError();
        char errorMsg[256];
        sprintf_s(errorMsg, sizeof(errorMsg),
            skCrypt("Failed to register window class!\nError code: %lu").decrypt(), error);
        ERROR_POPUP(NULL, errorMsg, skCrypt("Window Registration Failed").decrypt(), MB_OK | MB_ICONERROR);
        return -1;
    }

    main_hwnd = CreateWindowA(wc.lpszClassName, window_title, WS_POPUP,
                              0, 0, 1, 1,
                              NULL, NULL, wc.hInstance, NULL);

    if (!main_hwnd) {
        DWORD error = GetLastError();
        char errorMsg[256];
        sprintf_s(errorMsg, sizeof(errorMsg),
            skCrypt("Failed to create window!\nError code: %lu\nClass name: %s").decrypt(),
            error, window_class);
        ERROR_POPUP(NULL, errorMsg, skCrypt("Window Creation Failed").decrypt(), MB_OK | MB_ICONERROR);
        UnregisterClassA(wc.lpszClassName, wc.hInstance);
        return -1;
    }

    // Show a loading message
    ShowWindow(main_hwnd, SW_HIDE); // Keep window hidden but created

    // Allocate console for debugging
    AllocConsole();
    freopen_s((FILE**)stdout, skCrypt("CONOUT$").decrypt(), skCrypt("w").decrypt(), stdout);
    freopen_s((FILE**)stderr, skCrypt("CONOUT$").decrypt(), skCrypt("w").decrypt(), stderr);
    freopen_s((FILE**)stdin, skCrypt("CONIN$").decrypt(), skCrypt("r").decrypt(), stdin);
    SetConsoleTitleA(skCrypt("Debug Console").decrypt());

    // Print all running processes
    std::cout << skCrypt("=== RUNNING PROCESSES ===").decrypt() << std::endl;
    HANDLE hProcessSnap = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
    if (hProcessSnap != INVALID_HANDLE_VALUE) {
        PROCESSENTRY32 pe32;
        pe32.dwSize = sizeof(PROCESSENTRY32);

        if (Process32First(hProcessSnap, &pe32)) {
            do {
                std::cout << skCrypt("PID: ").decrypt() << pe32.th32ProcessID << skCrypt(" - ").decrypt() << pe32.szExeFile << std::endl;
            } while (Process32Next(hProcessSnap, &pe32));
        }
        CloseHandle(hProcessSnap);
    }
    std::cout << skCrypt("=========================").decrypt() << std::endl << std::endl;

    // Initialize silent termination system FIRST (before any security checks)
    IndirectCrash::SilentTermination::SetupSilentMode();

    // Initialize advanced obfuscation system
    SecurityCore::RuntimeConfig::Initialize();
    AdvancedObfuscation::PolymorphicSecurity::InitializeMorphState();
    AdvancedObfuscation::AntiPatchProtection::InitializeFunctionChecksums();

    // Initialize enhanced integrity protection
    INIT_INTEGRITY();

    // Protect critical functions from patching
    PROTECT_FUNCTION(WinMain);
    PROTECT_FUNCTION(AdvancedProtection::AntiDebug::IsDebuggerAttached);

    // NOW run security checks ONE BY ONE - show detailed error for each failure
    // Distributed junk code and security checks throughout

    RANDOM_JUNK(); // Execute random junk code with hidden security checks

    // Integrity check before security tests
    QUICK_INTEGRITY_CHECK();

    // Test 1: Basic Debugger Detection
    SECURITY_POPUP(NULL, skCrypt("Testing: Basic Debugger Detection...").decrypt(), skCrypt("Security Check 1/9").decrypt(), MB_OK);

    COMPLEX_JUNK_1(); // More junk code with hidden checks
    INTEGRITY_GUARD(); // Inline integrity check

    if (AdvancedProtection::AntiDebug::IsDebuggerAttached()) {
        char msg[1024];
        sprintf_s(msg, sizeof(msg),
            skCrypt("SECURITY CHECK FAILED!\n\n"
            "DETECTED: Basic Debugger\n"
            "REASON: %s\n\n"
            "SOLUTION:\n"
            "- Close Visual Studio completely\n"
            "- Close x64dbg, OllyDbg, or other debuggers\n"
            "- Run the .exe from Windows Explorer, not from IDE\n"
            "- Make sure no debugging tools are running in Task Manager").decrypt(),
            AdvancedProtection::AntiDebug::GetDetectionReason() ?
            AdvancedProtection::AntiDebug::GetDetectionReason() : skCrypt("Unknown debugger detected").decrypt());
        SECURITY_POPUP(NULL, msg, skCrypt("Security Detection - Basic Debugger").decrypt(), MB_OK | MB_ICONERROR);
        return -1;
    }

    COMPLEX_JUNK_2(); // Hidden security checks in junk code

    // Test 2: Process Debugging Check
    SECURITY_POPUP(NULL, skCrypt("Testing: Debugging Processes...").decrypt(), skCrypt("Security Check 2/9").decrypt(), MB_OK);

    MORPH_SECURITY_CHECK(); // Polymorphic security check

    if (AdvancedProtection::AntiDebug::CheckDebuggingProcesses()) {
        char msg[1024];
        sprintf_s(msg, sizeof(msg),
            skCrypt("SECURITY CHECK FAILED!\n\n"
            "DETECTED: Debugging Tool Process\n"
            "REASON: %s\n\n"
            "SOLUTION:\n"
            "- Close the detected debugging tool completely\n"
            "- Check Task Manager for debugging processes\n"
            "- Restart your computer if tools were recently closed\n"
            "- Make sure no reverse engineering tools are running").decrypt(),
            AdvancedProtection::AntiDebug::GetDetectionReason() ?
            AdvancedProtection::AntiDebug::GetDetectionReason() : skCrypt("Unknown debugging process detected").decrypt());
        SECURITY_POPUP(NULL, msg, skCrypt("Security Detection - Debugging Process").decrypt(), MB_OK | MB_ICONERROR);
        return -1;
    }

    COMPLEX_JUNK_3(); // More distributed security checks
    VERIFY_INTEGRITY(); // Anti-patch verification

    // Test 3: VM Detection
    SECURITY_POPUP(NULL, skCrypt("Testing: Virtual Machine Detection...").decrypt(), skCrypt("Security Check 3/9").decrypt(), MB_OK);

    RANDOM_JUNK(); // Random junk code selection

    if (AdvancedProtection::VMDetection::DetectVM()) {
        char msg[1024];
        sprintf_s(msg, sizeof(msg),
            skCrypt("SECURITY CHECK FAILED!\n\n"
            "DETECTED: Virtual Machine Environment\n"
            "REASON: %s\n\n"
            "SOLUTION:\n"
            "• Run on physical hardware for full functionality\n"
            "• VM detection is now dynamically calculated based on system characteristics\n"
            "• For development, run from an IDE like Visual Studio to auto-disable VM checks\n"
            "• The new security system adapts to development environments automatically").decrypt(),
            AdvancedProtection::AntiDebug::GetDetectionReason() ?
            AdvancedProtection::AntiDebug::GetDetectionReason() : skCrypt("Virtual machine detected").decrypt());
        SECURITY_POPUP(NULL, msg, skCrypt("Security Detection - Virtual Machine").decrypt(), MB_OK | MB_ICONERROR);
        return -1;
    }

    // Test 4: PEB Flags
    SECURITY_POPUP(NULL, skCrypt("Testing: PEB Debugging Flags...").decrypt(), skCrypt("Security Check 4/9").decrypt(), MB_OK);
    if (AdvancedProtection::AntiDebug::CheckPEBFlags()) {
        SECURITY_POPUP(NULL,
            skCrypt("SECURITY CHECK FAILED!\n\n"
            "DETECTED: PEB Debugging Flags\n"
            "REASON: Process Environment Block indicates debugging\n\n"
            "SOLUTION:\n"
            "• Close all debugging tools completely\n"
            "• Some tools modify PEB flags even after closing\n"
            "• Restart the program after closing debuggers\n"
            "• Make sure no debugger was attached to this process").decrypt(),
            skCrypt("Security Detection - PEB Flags").decrypt(), MB_OK | MB_ICONERROR);
        return -1;
    }

    // Test 5: Hardware Breakpoints
    SECURITY_POPUP(NULL, skCrypt("Testing: Hardware Breakpoints...").decrypt(), skCrypt("Security Check 5/9").decrypt(), MB_OK);
    if (AdvancedProtection::AntiDebug::CheckHardwareBreakpoints()) {
        SECURITY_POPUP(NULL,
            skCrypt("SECURITY CHECK FAILED!\n\n"
            "DETECTED: Hardware Breakpoints\n"
            "REASON: Debug registers contain breakpoints\n\n"
            "SOLUTION:\n"
            "• Clear all breakpoints in your debugger\n"
            "• Close debugger completely and restart it\n"
            "• Hardware breakpoints persist until cleared\n"
            "• Make sure no debugger has set hardware breakpoints").decrypt(),
            skCrypt("Security Detection - Hardware Breakpoints").decrypt(), MB_OK | MB_ICONERROR);
        return -1;
    }

    // If we get here, basic checks passed
    SECURITY_POPUP(NULL, skCrypt("Basic security checks passed!\nContinuing with advanced checks...").decrypt(), skCrypt("Security Status").decrypt(), MB_OK | MB_ICONINFORMATION);

    // Test 6: Integrity Checker
    SECURITY_POPUP(NULL, skCrypt("Testing: Binary Integrity Check...").decrypt(), skCrypt("Security Check 6/8").decrypt(), MB_OK);
    try {
        Obfuscation::IntegrityChecker::InitializeChecksum();
        SECURITY_POPUP(NULL, skCrypt("Integrity check passed!").decrypt(), skCrypt("Security Check 6/8 - Success").decrypt(), MB_OK);
    }
    catch (...) {
        SECURITY_POPUP(NULL,
            skCrypt("SECURITY CHECK FAILED!\n\n"
            "DETECTED: Binary Integrity Violation\n"
            "REASON: File checksum mismatch or corruption\n\n"
            "SOLUTION:\n"
            "• Re-download the original program\n"
            "• Check for file corruption\n"
            "• Antivirus might have modified the file\n"
            "• Disable real-time protection temporarily").decrypt(),
            skCrypt("Security Detection - Integrity").decrypt(), MB_OK | MB_ICONERROR);
        return -1;
    }

    JUNK_CRYPTO_OP(); // Fake crypto with hidden security checks

    // Test 7: Stack Protection
    SECURITY_POPUP(NULL, skCrypt("Testing: Stack Protection...").decrypt(), skCrypt("Security Check 7/8").decrypt(), MB_OK);

    JUNK_NETWORK_OP(); // Fake network operations

    try {
        Obfuscation::StackProtection::InitializeCanary();
        SECURITY_POPUP(NULL, skCrypt("Stack protection initialized!").decrypt(), skCrypt("Security Check 7/8 - Success").decrypt(), MB_OK);
    }
    catch (...) {
        SECURITY_POPUP(NULL,
            skCrypt("SECURITY CHECK FAILED!\n\n"
            "DETECTED: Stack Protection Error\n"
            "REASON: Cannot initialize stack canary\n\n"
            "SOLUTION:\n"
            "• This is usually a system-level issue\n"
            "• Try running as administrator\n"
            "• Check if DEP/ASLR is interfering\n"
            "• Restart the program").decrypt(),
            skCrypt("Security Detection - Stack Protection").decrypt(), MB_OK | MB_ICONERROR);
        return -1;
    }

    JUNK_FILE_OP(); // Fake file operations with hidden checks

    // Test 8: Final Success
    SECURITY_POPUP(NULL,
        skCrypt("ALL SECURITY CHECKS PASSED!\n\n"
        "Basic debugger detection\n"
        "Process scanning\n"
        "Virtual machine detection\n"
        "PEB flags check\n"
        "Hardware breakpoints\n"
        "Binary integrity\n"
        "Stack protection\n\n"
        "Program will now continue to main interface...").decrypt(),
        skCrypt("Security Status - All Checks Passed!").decrypt(), MB_OK | MB_ICONINFORMATION);

    // Initialize advanced protection system with distributed checks
    JUNK_REGISTRY_OP(); // Fake registry operations with security verification
    MORPH_SECURITY_CHECK(); // Polymorphic security state change
    VERIFY_INTEGRITY(); // Anti-patch protection verification
    RANDOM_JUNK(); // Final random junk code execution

    // Continue with GUI initialization step by step
    // Setup message removed - only show errors

    // Initialize Direct3D
    if (!CreateDeviceD3D(main_hwnd)) {
        // Get more specific error information
        char errorMsg[512];
        sprintf_s(errorMsg, sizeof(errorMsg),
            skCrypt("DIRECT3D INITIALIZATION FAILED!\n\n"
            "Window Handle: %p\n"
            "Window Valid: %s\n"
            "DirectX Available: %s\n\n"
            "SOLUTIONS:\n"
            "• Update graphics drivers\n"
            "• Install DirectX End-User Runtime\n"
            "• Try running as administrator\n"
            "• Check Windows Event Viewer for driver errors").decrypt(),
            main_hwnd,
            IsWindow(main_hwnd) ? skCrypt("Yes").decrypt() : skCrypt("No").decrypt(),
            (Direct3DCreate9(D3D_SDK_VERSION) != NULL) ? skCrypt("Yes").decrypt() : skCrypt("No").decrypt());

        ERROR_POPUP(NULL, errorMsg, skCrypt("Direct3D Failed").decrypt(), MB_OK | MB_ICONERROR);
        CleanupDeviceD3D();
        UnregisterClassA(wc.lpszClassName, wc.hInstance);
        return 1;
    }

    // Success message removed - only show errors

    // Show the window
    ShowWindow(main_hwnd, SW_HIDE);
    UpdateWindow(main_hwnd);
    // Success message removed - only show errors

    // Setup Dear ImGui context

    try {
        ImGui::CreateContext();
        // Success message removed - only show errors
    }
    catch (...) {
        ERROR_POPUP(NULL,
            skCrypt("IMGUI INITIALIZATION FAILED!\n\n"
            "Cannot create ImGui context.\n\n"
            "SOLUTIONS:\n"
            "• Make sure ImGui libraries are properly linked\n"
            "• Check for missing DLL files\n"
            "• Try running as administrator").decrypt(),
            skCrypt("Error - ImGui Failed").decrypt(), MB_OK | MB_ICONERROR);
        return 1;
    }

    // Setup message removed - only show errors

    try {
        ImGuiIO& io = ImGui::GetIO();
        io.IniFilename = nullptr; //crucial for not leaving the imgui.ini file
        io.ConfigFlags |= ImGuiConfigFlags_ViewportsEnable; // Enable Multi-Viewport / Platform Windows

        // When viewports are enabled we tweak WindowRounding/WindowBg so platform windows can look identical to regular ones.
        ImGuiStyle& style = ImGui::GetStyle();
        if (io.ConfigFlags & ImGuiConfigFlags_ViewportsEnable)
        {
            style.WindowRounding = 0.0f;
            style.Colors[ImGuiCol_WindowBg].w = 1.0f;
        }

        // Success message removed - only show errors
    }
    catch (...) {
        ERROR_POPUP(NULL,
            skCrypt("IMGUI CONFIGURATION FAILED!\n\n"
            "Cannot configure ImGui settings.\n\n"
            "This might be a memory or compatibility issue.").decrypt(),
            skCrypt("Error - ImGui Config Failed").decrypt(), MB_OK | MB_ICONERROR);
        return 1;
    }

    // Setup Platform/Renderer backends
    // Setup message removed - only show errors

    try {
        ImGui_ImplWin32_Init(main_hwnd);
        ImGui_ImplDX9_Init(g_pd3dDevice);

        // Show security popup status
        #if DISABLE_SECURITY_POPUPS
            INFO_POPUP(NULL, skCrypt("Security Popup Status: DISABLED\n\nSecurity checks are running but popups are hidden.").decrypt(), skCrypt("Security Configuration").decrypt(), MB_OK | MB_ICONINFORMATION);
        #else
            INFO_POPUP(NULL, skCrypt("Security Popup Status: ENABLED\n\nAll security popups will be shown.").decrypt(), skCrypt("Security Configuration").decrypt(), MB_OK | MB_ICONINFORMATION);
        #endif

        INFO_POPUP(NULL,
            skCrypt("GUI INITIALIZATION COMPLETE!\n\n"
            "All security checks passed\n"
            "Direct3D initialized\n"
            "Window created\n"
            "ImGui context created\n"
            "ImGui backends initialized\n\n"
            "Starting main application loop...").decrypt(),
            skCrypt("Success - Ready to Start!").decrypt(), MB_OK | MB_ICONINFORMATION);
    }
    catch (...) {
        MessageBoxA(NULL,
            skCrypt("IMGUI BACKEND INITIALIZATION FAILED!\n\n"
            "Cannot initialize ImGui Win32/DX9 backends.\n\n"
            "SOLUTIONS:\n"
            "• Check DirectX installation\n"
            "• Update graphics drivers\n"
            "• Try running as administrator").decrypt(),
            skCrypt("Error - ImGui Backends Failed").decrypt(), MB_OK | MB_ICONERROR);
        return 1;
    }

    // Load Fonts
    // - If no fonts are loaded, dear imgui will use the default font. You can also load multiple fonts and use ImGui::PushFont()/PopFont() to select them.
    // - AddFontFromFileTTF() will return the ImFont* so you can store it if you need to select the font among multiple.
    // - If the file cannot be loaded, the function will return NULL. Please handle those errors in your application (e.g. use an assertion, or display an error and quit).
    // - The fonts will be rasterized at a given size (w/ oversampling) and stored into a texture when calling ImFontAtlas::Build()/GetTexDataAsXXXX(), which ImGui_ImplXXXX_NewFrame below will call.
    // - Read 'docs/FONTS.md' for more instructions and details.
    // - Remember that in C/C++ if you want to include a backslash \ in a string literal you need to write a double backslash \\ !
    //io.Fonts->AddFontDefault();
    //io.Fonts->AddFontFromFileTTF("../../misc/fonts/Roboto-Medium.ttf", 16.0f);
    //io.Fonts->AddFontFromFileTTF("../../misc/fonts/Cousine-Regular.ttf", 15.0f);
    //io.Fonts->AddFontFromFileTTF("../../misc/fonts/DroidSans.ttf", 16.0f);
    //io.Fonts->AddFontFromFileTTF("../../misc/fonts/ProggyTiny.ttf", 10.0f);
    //ImFont* font = io.Fonts->AddFontFromFileTTF("c:\\Windows\\Fonts\\ArialUni.ttf", 18.0f, NULL, io.Fonts->GetGlyphRangesJapanese());
    //IM_ASSERT(font != NULL);

    // Main loop
    MSG msg;
    ZeroMemory(&msg, sizeof(msg));
    int frame_counter = 0;

    // Keep the main window hidden - it's only for DirectX
    ShowWindow(main_hwnd, SW_HIDE);
    UpdateWindow(main_hwnd);

    while (msg.message != WM_QUIT)
    {
        // Lightweight frame counting instead of heavy security checks
        frame_counter++;

        // ENHANCED Runtime security checks - ALWAYS ENABLED
        if (frame_counter % 300 == 0) {  // Every 5 seconds at 60fps - more frequent
            // Comprehensive runtime security checks

            // Check 1: Basic debugger detection (SILENT)
            if (AdvancedProtection::AntiDebug::IsDebuggerAttached()) {
                // No message boxes - immediate silent termination
                IndirectCrash::SilentTermination::SilentExit();
            }

            // Check 2: Process scanning for debugging tools (SILENT)
            if (AdvancedProtection::AntiDebug::CheckDebuggingProcesses()) {
                // No message boxes - immediate silent termination
                IndirectCrash::SilentTermination::SilentExit();
            }
        }

        // More frequent checks for critical security (SILENT)
        if (frame_counter % 60 == 0) {  // Every second
            // Quick PEB check for debugger attachment - SILENT
            try {
                PPEB peb = (PPEB)__readgsqword(0x60);
                if (peb && peb->BeingDebugged) {
                    // No message boxes - immediate silent termination
                    IndirectCrash::SilentTermination::SilentExit();
                }
            }
            catch (...) {
                // No message boxes - immediate silent termination
                IndirectCrash::SilentTermination::SilentExit();
            }
        }

        // Keep existing advanced checks
        ANTI_HOOK_CHECK();             // API hooking detection
        TIMING_CHECK();                // Timing analysis
        FLOW_OBFUSCATE();             // Flow obfuscation
        

        
        if (PeekMessage(&msg, NULL, 0U, 0U, PM_REMOVE))
        {
            TranslateMessage(&msg);
            DispatchMessage(&msg);
            continue;
        }
        
        // Start the Dear ImGui frame
        ImGui_ImplDX9_NewFrame();
        ImGui_ImplWin32_NewFrame();
        ImGui::NewFrame();
        {
            static int init = false;
            if (!init) {
                // Skip problematic security macros - already checked at startup
                ui::init(g_pd3dDevice);
                init = true;
            }
            else {
                ui::render();
            }
        }
        ImGui::EndFrame();

        g_pd3dDevice->Clear(0, NULL, D3DCLEAR_TARGET | D3DCLEAR_ZBUFFER, 0, 1.0f, 0);
        if (g_pd3dDevice->BeginScene() >= 0)
        {
            ImGui::Render();
            ImGui_ImplDX9_RenderDrawData(ImGui::GetDrawData());
            g_pd3dDevice->EndScene();
        }

        // Update and Render additional Platform Windows
        ImGuiIO& io = ImGui::GetIO();
        if (io.ConfigFlags & ImGuiConfigFlags_ViewportsEnable)
        {
            ImGui::UpdatePlatformWindows();
            ImGui::RenderPlatformWindowsDefault();
        }

        HRESULT result = g_pd3dDevice->Present(NULL, NULL, NULL, NULL);

        // Handle loss of D3D9 device
        if (result == D3DERR_DEVICELOST && g_pd3dDevice->TestCooperativeLevel() == D3DERR_DEVICENOTRESET) {
            ResetDevice();
        }
        if (!globals.active) {
            msg.message = WM_QUIT;
        }
        
    }
    

    ImGui_ImplDX9_Shutdown();
    ImGui_ImplWin32_Shutdown();
    ImGui::DestroyContext();

    CleanupDeviceD3D();
    DestroyWindow(main_hwnd);
    UnregisterClassA(wc.lpszClassName, wc.hInstance);

    // Skip problematic shutdown macro
    // ADVANCED_PROTECTION_SHUTDOWN();

    // Free console
    FreeConsole();

    // Clear sensitive memory
    globals.ClearSensitiveData();

    return 0;
}

LRESULT CALLBACK WndProc(HWND hWnd, UINT msg, WPARAM wParam, LPARAM lParam) {
    if (ImGui_ImplWin32_WndProcHandler(hWnd, msg, wParam, lParam))
        return true;

    switch (msg)
    {
    case WM_SIZE:
        if (g_pd3dDevice != NULL && wParam != SIZE_MINIMIZED)
        {
            g_d3dpp.BackBufferWidth = LOWORD(lParam);
            g_d3dpp.BackBufferHeight = HIWORD(lParam);
            ResetDevice();
        }
        return 0;
    case WM_SYSCOMMAND:
        if ((wParam & 0xfff0) == SC_KEYMENU) // Disable ALT application menu
            return 0;
        break;
    case WM_DESTROY:
        PostQuitMessage(0);
        return 0;
    }
    return DefWindowProc(hWnd, msg, wParam, lParam);
}