﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="external\imgui\imgui.cpp">
      <Filter>External\ImGui</Filter>
    </ClCompile>
    <ClCompile Include="external\imgui\imgui_demo.cpp">
      <Filter>External\ImGui</Filter>
    </ClCompile>
    <ClCompile Include="external\imgui\imgui_draw.cpp">
      <Filter>External\ImGui</Filter>
    </ClCompile>
    <ClCompile Include="external\imgui\imgui_impl_dx9.cpp">
      <Filter>External\ImGui</Filter>
    </ClCompile>
    <ClCompile Include="external\imgui\imgui_impl_win32.cpp">
      <Filter>External\ImGui</Filter>
    </ClCompile>
    <ClCompile Include="external\imgui\imgui_widgets.cpp">
      <Filter>External\ImGui</Filter>
    </ClCompile>
    <ClCompile Include="external\imgui\imgui_tables.cpp">
      <Filter>External\ImGui</Filter>
    </ClCompile>
    <ClCompile Include="src\Main.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ui\ui.cpp">
      <Filter>Source Files\UI</Filter>
    </ClCompile>
    <ClCompile Include="src\security\config.cpp" />
    <ClCompile Include="src\security\debug.cpp" />
    <ClCompile Include="src\security\security.cpp" />
    <ClCompile Include="src\security\core.cpp" />
    <ClCompile Include="src\security\indirect_crash.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="external\imgui\imconfig.h">
      <Filter>External\ImGui</Filter>
    </ClInclude>
    <ClInclude Include="external\imgui\imgui.h">
      <Filter>External\ImGui</Filter>
    </ClInclude>
    <ClInclude Include="external\imgui\imgui_impl_dx9.h">
      <Filter>External\ImGui</Filter>
    </ClInclude>
    <ClInclude Include="external\imgui\imgui_impl_win32.h">
      <Filter>External\ImGui</Filter>
    </ClInclude>
    <ClInclude Include="external\imgui\imgui_internal.h">
      <Filter>External\ImGui</Filter>
    </ClInclude>
    <ClInclude Include="external\imgui\imstb_rectpack.h">
      <Filter>External\ImGui</Filter>
    </ClInclude>
    <ClInclude Include="external\imgui\imstb_textedit.h">
      <Filter>External\ImGui</Filter>
    </ClInclude>
    <ClInclude Include="external\imgui\imstb_truetype.h">
      <Filter>External\ImGui</Filter>
    </ClInclude>
    <ClInclude Include="src\Main.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\ui\ui.hpp">
      <Filter>Header Files\UI</Filter>
    </ClInclude>
    <ClInclude Include="src\globals.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\auth\auth.hpp">
      <Filter>Header Files\Auth</Filter>
    </ClInclude>
    <ClInclude Include="src\auth\skStr.h">
      <Filter>Header Files\Auth</Filter>
    </ClInclude>
    <ClInclude Include="src\auth\utils.hpp">
      <Filter>Header Files\Auth</Filter>
    </ClInclude>
    <ClInclude Include="src\auth\json.hpp">
      <Filter>Header Files\Auth</Filter>
    </ClInclude>
    <ClInclude Include="src\auth\secure_auth.hpp" />
    <ClInclude Include="src\security\config.hpp" />
    <ClInclude Include="src\security\obfuscation.hpp" />
    <ClInclude Include="src\security\protection.hpp" />
    <ClInclude Include="src\security\debug.hpp" />
    <ClInclude Include="src\security\encryption.hpp" />
    <ClInclude Include="src\security\integrity.hpp" />
    <ClInclude Include="src\security\security.hpp" />
    <ClInclude Include="src\security\settings.h" />
    <ClInclude Include="src\security\core.hpp" />
    <ClInclude Include="src\security\macros.hpp" />
    <ClInclude Include="src\security\strings.hpp" />
    <ClInclude Include="src\security\indirect_crash.hpp" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="External">
      <UniqueIdentifier>{8cf77124-bebc-471d-acac-8b9b1ffc2a66}</UniqueIdentifier>
    </Filter>
    <Filter Include="External\ImGui">
      <UniqueIdentifier>{1a2b3c4d-5e6f-7890-abcd-ef1234567890}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\Auth">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\Security">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\UI">
      <UniqueIdentifier>{39987C36-1234-5678-9ABC-DEF012345678}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995381-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\Auth">
      <UniqueIdentifier>{67DA6AB7-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\Security">
      <UniqueIdentifier>{39987C37-1234-5678-9ABC-DEF012345678}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\UI">
      <UniqueIdentifier>{12345678-9ABC-DEF0-1234-56789ABCDEF0}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>